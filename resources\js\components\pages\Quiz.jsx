import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import axios from 'axios';

const Quiz = () => {
    const { id } = useParams();
    const navigate = useNavigate();
    const [quiz, setQuiz] = useState(null);
    const [answers, setAnswers] = useState({});
    const [loading, setLoading] = useState(true);
    const [submitting, setSubmitting] = useState(false);
    const [results, setResults] = useState(null);
    const [error, setError] = useState(null);

    useEffect(() => {
        fetchQuiz();
    }, [id]);

    const fetchQuiz = async () => {
        try {
            const response = await axios.get(`/api/quiz/${id}`);
            setQuiz(response.data);
        } catch (error) {
            console.error('Error fetching quiz:', error);
            setError('Erreur lors du chargement du quiz');
        } finally {
            setLoading(false);
        }
    };

    const handleAnswerChange = (questionId, answerIndex) => {
        setAnswers({
            ...answers,
            [questionId]: answerIndex
        });
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        setSubmitting(true);
        
        try {
            const response = await axios.post(`/api/quiz/${id}/submit`, {
                answers: answers
            });
            setResults(response.data);
        } catch (error) {
            console.error('Error submitting quiz:', error);
            setError('Erreur lors de la soumission du quiz');
        } finally {
            setSubmitting(false);
        }
    };

    const getScoreColor = (score) => {
        if (score >= 80) return 'text-green-600';
        if (score >= 60) return 'text-yellow-600';
        return 'text-red-600';
    };

    const getScoreBgColor = (score) => {
        if (score >= 80) return 'bg-green-100 border-green-200';
        if (score >= 60) return 'bg-yellow-100 border-yellow-200';
        return 'bg-red-100 border-red-200';
    };

    if (loading) {
        return (
            <div className="min-h-screen flex items-center justify-center">
                <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-red-600"></div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="min-h-screen flex items-center justify-center">
                <div className="text-center">
                    <p className="text-red-600 mb-4">{error}</p>
                    <Link to="/dashboard" className="text-red-600 hover:text-red-800">
                        Retour au tableau de bord
                    </Link>
                </div>
            </div>
        );
    }

    if (!quiz) {
        return (
            <div className="min-h-screen flex items-center justify-center">
                <div className="text-center">
                    <p className="text-gray-600 mb-4">Quiz non trouvé</p>
                    <Link to="/dashboard" className="text-red-600 hover:text-red-800">
                        Retour au tableau de bord
                    </Link>
                </div>
            </div>
        );
    }

    // Results view
    if (results) {
        return (
            <div className="min-h-screen bg-gray-50 py-8">
                <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="bg-white rounded-lg shadow-md p-6 mb-8">
                        <div className="text-center mb-6">
                            <h1 className="text-3xl font-bold text-gray-900 mb-4">
                                Résultats du Quiz
                            </h1>
                            
                            <div className={`inline-block p-6 rounded-lg border-2 ${getScoreBgColor(results.score)}`}>
                                <div className={`text-4xl font-bold ${getScoreColor(results.score)} mb-2`}>
                                    {results.score}%
                                </div>
                                <div className="text-gray-700">
                                    {results.correct_answers} / {results.total_questions} bonnes réponses
                                </div>
                                <div className="text-sm text-gray-600 mt-2">
                                    {results.passed ? '✅ Quiz réussi !' : `❌ Score minimum requis: ${results.passing_score}%`}
                                </div>
                            </div>
                        </div>

                        <div className="space-y-6">
                            {results.results.map((result, index) => (
                                <div key={result.question_id} className="border border-gray-200 rounded-lg p-4">
                                    <div className="flex items-start justify-between mb-3">
                                        <h3 className="font-medium text-gray-900">
                                            Question {index + 1}: {result.question}
                                        </h3>
                                        <span className={`text-xl ${result.is_correct ? 'text-green-600' : 'text-red-600'}`}>
                                            {result.is_correct ? '✅' : '❌'}
                                        </span>
                                    </div>
                                    
                                    <div className="text-sm text-gray-600 space-y-1">
                                        <p>
                                            <strong>Votre réponse:</strong> {result.user_answer !== null ? `Option ${result.user_answer + 1}` : 'Aucune réponse'}
                                        </p>
                                        <p>
                                            <strong>Bonne réponse:</strong> Option {result.correct_answer + 1}
                                        </p>
                                        {result.explanation && (
                                            <p className="mt-2 p-2 bg-blue-50 rounded">
                                                <strong>Explication:</strong> {result.explanation}
                                            </p>
                                        )}
                                    </div>
                                </div>
                            ))}
                        </div>

                        <div className="mt-8 text-center space-x-4">
                            <Link
                                to="/dashboard"
                                className="bg-red-600 hover:bg-red-700 text-white px-6 py-3 rounded-md font-medium transition-colors"
                            >
                                Retour au tableau de bord
                            </Link>
                            {!results.passed && (
                                <button
                                    onClick={() => {
                                        setResults(null);
                                        setAnswers({});
                                    }}
                                    className="bg-gray-600 hover:bg-gray-700 text-white px-6 py-3 rounded-md font-medium transition-colors"
                                >
                                    Refaire le quiz
                                </button>
                            )}
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    // Quiz form view
    return (
        <div className="min-h-screen bg-gray-50 py-8">
            <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
                <div className="mb-8">
                    <Link 
                        to="/dashboard" 
                        className="text-red-600 hover:text-red-800 mb-4 inline-flex items-center"
                    >
                        ← Retour au tableau de bord
                    </Link>
                    
                    <div className="bg-white rounded-lg shadow-md p-6">
                        <h1 className="text-3xl font-bold text-gray-900 mb-2">
                            {quiz.title}
                        </h1>
                        <p className="text-gray-600">
                            {quiz.description}
                        </p>
                        <p className="text-sm text-gray-500 mt-2">
                            Score minimum requis: {quiz.passing_score}%
                        </p>
                    </div>
                </div>

                <form onSubmit={handleSubmit} className="space-y-6">
                    {quiz.questions.map((question, questionIndex) => (
                        <div key={question.id} className="bg-white rounded-lg shadow-md p-6">
                            <h3 className="text-lg font-medium text-gray-900 mb-4">
                                Question {questionIndex + 1}: {question.question}
                            </h3>
                            
                            <div className="space-y-3">
                                {question.options.map((option, optionIndex) => (
                                    <label key={optionIndex} className="flex items-center cursor-pointer">
                                        <input
                                            type="radio"
                                            name={`question_${question.id}`}
                                            value={optionIndex}
                                            checked={answers[question.id] === optionIndex}
                                            onChange={() => handleAnswerChange(question.id, optionIndex)}
                                            className="mr-3 text-red-600 focus:ring-red-500"
                                        />
                                        <span className="text-gray-700">{option}</span>
                                    </label>
                                ))}
                            </div>
                        </div>
                    ))}

                    <div className="bg-white rounded-lg shadow-md p-6">
                        <button
                            type="submit"
                            disabled={submitting || Object.keys(answers).length !== quiz.questions.length}
                            className="w-full bg-red-600 hover:bg-red-700 text-white py-3 px-6 rounded-md font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                            {submitting ? 'Soumission...' : 'Soumettre le quiz'}
                        </button>
                        
                        {Object.keys(answers).length !== quiz.questions.length && (
                            <p className="text-sm text-gray-500 text-center mt-2">
                                Veuillez répondre à toutes les questions avant de soumettre
                            </p>
                        )}
                    </div>
                </form>
            </div>
        </div>
    );
};

export default Quiz;

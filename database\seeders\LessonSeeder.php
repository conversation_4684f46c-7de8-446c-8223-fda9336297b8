<?php

namespace Database\Seeders;

use App\Models\Lesson;
use App\Models\Quiz;
use App\Models\Question;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class LessonSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Leçon 1: Les salutations
        $lesson1 = Lesson::create([
            'title' => 'Les salutations en allemand',
            'level' => 'A1.1',
            'content' => "Bienvenue dans votre première leçon d'allemand ! 🇩🇪

Aujourd'hui, nous allons apprendre les salutations de base en allemand.

**Les salutations courantes :**

• **Hallo** - Salut (informel)
• **Guten Tag** - Bonjour (formel)
• **Guten Morgen** - Bonjour (le matin)
• **Guten Abend** - Bonsoir
• **Auf Wiedersehen** - Au revoir (formel)
• **Tschüss** - Au revoir (informel)

**Comment se présenter :**

• **Ich heiße...** - Je m'appelle...
• **Mein Name ist...** - Mon nom est...
• **Wie heißen Sie?** - Comment vous appelez-vous ? (formel)
• **Wie heißt du?** - Comment tu t'appelles ? (informel)

**Exemples de dialogue :**

A: Hallo! Ich heiße Anna. Wie heißt du?
B: Hallo Anna! Ich heiße Max. Freut mich!
A: Freut mich auch!

**Prononciation :**
- 'ch' dans 'ich' se prononce comme un 'sch' doux
- 'ß' (eszett) se prononce comme 'ss'
- Les voyelles avec tréma (ä, ö, ü) ont des sons spéciaux

Pratiquez ces expressions et passez au quiz pour tester vos connaissances !",
            'audio_url' => '/audio/lesson1.mp3',
            'order' => 1,
            'is_active' => true,
        ]);

        // Quiz pour la leçon 1
        $quiz1 = Quiz::create([
            'lesson_id' => $lesson1->id,
            'title' => 'Quiz : Les salutations',
            'description' => 'Testez vos connaissances sur les salutations allemandes',
            'passing_score' => 70,
        ]);

        // Questions pour le quiz 1
        Question::create([
            'quiz_id' => $quiz1->id,
            'question' => 'Comment dit-on "Bonjour" de manière formelle en allemand ?',
            'options' => ['Hallo', 'Guten Tag', 'Tschüss', 'Danke'],
            'correct_answer' => 1,
            'explanation' => '"Guten Tag" est la salutation formelle pour dire bonjour en allemand.',
            'order' => 1,
        ]);

        Question::create([
            'quiz_id' => $quiz1->id,
            'question' => 'Que signifie "Wie heißen Sie?" ?',
            'options' => ['Au revoir', 'Comment allez-vous ?', 'Comment vous appelez-vous ?', 'Merci beaucoup'],
            'correct_answer' => 2,
            'explanation' => '"Wie heißen Sie?" est la façon formelle de demander le nom de quelqu\'un.',
            'order' => 2,
        ]);

        Question::create([
            'quiz_id' => $quiz1->id,
            'question' => 'Quelle est la différence entre "du" et "Sie" ?',
            'options' => ['Aucune différence', '"du" est informel, "Sie" est formel', '"du" est formel, "Sie" est informel', 'Ce sont des synonymes'],
            'correct_answer' => 1,
            'explanation' => '"du" s\'utilise avec les amis et la famille (informel), "Sie" avec les inconnus et dans un contexte professionnel (formel).',
            'order' => 3,
        ]);

        // Leçon 2: Les nombres
        $lesson2 = Lesson::create([
            'title' => 'Les nombres de 0 à 20',
            'level' => 'A1.1',
            'content' => "Apprenons les nombres de base en allemand ! 🔢

**Les nombres de 0 à 10 :**

• **null** - 0
• **eins** - 1
• **zwei** - 2
• **drei** - 3
• **vier** - 4
• **fünf** - 5
• **sechs** - 6
• **sieben** - 7
• **acht** - 8
• **neun** - 9
• **zehn** - 10

**Les nombres de 11 à 20 :**

• **elf** - 11
• **zwölf** - 12
• **dreizehn** - 13
• **vierzehn** - 14
• **fünfzehn** - 15
• **sechzehn** - 16
• **siebzehn** - 17
• **achtzehn** - 18
• **neunzehn** - 19
• **zwanzig** - 20

**Utilisation pratique :**

• **Wie alt sind Sie?** - Quel âge avez-vous ?
• **Ich bin ... Jahre alt** - J'ai ... ans
• **Wie viel kostet das?** - Combien ça coûte ?
• **Das kostet ... Euro** - Ça coûte ... euros

**Exemples :**
- Ich bin achtzehn Jahre alt. (J'ai 18 ans)
- Das kostet fünf Euro. (Ça coûte 5 euros)
- Meine Telefonnummer ist null-eins-zwei-drei. (Mon numéro est 0123)

**Astuce de prononciation :**
- 'ei' se prononce 'aï' (comme dans 'eins', 'zwei')
- 'ie' se prononce 'i' long (comme dans 'sieben')
- 'ü' se prononce comme un 'u' français avec les lèvres arrondies",
            'audio_url' => '/audio/lesson2.mp3',
            'order' => 2,
            'is_active' => true,
        ]);

        // Quiz pour la leçon 2
        $quiz2 = Quiz::create([
            'lesson_id' => $lesson2->id,
            'title' => 'Quiz : Les nombres',
            'description' => 'Testez vos connaissances sur les nombres allemands',
            'passing_score' => 70,
        ]);

        // Questions pour le quiz 2
        Question::create([
            'quiz_id' => $quiz2->id,
            'question' => 'Comment dit-on "15" en allemand ?',
            'options' => ['fünfzehn', 'vierzehn', 'sechzehn', 'siebzehn'],
            'correct_answer' => 0,
            'explanation' => '"fünfzehn" signifie 15 en allemand (fünf = 5, zehn = 10).',
            'order' => 1,
        ]);

        Question::create([
            'quiz_id' => $quiz2->id,
            'question' => 'Que signifie "Wie alt sind Sie?" ?',
            'options' => ['Comment vous appelez-vous ?', 'Quel âge avez-vous ?', 'Où habitez-vous ?', 'Que faites-vous ?'],
            'correct_answer' => 1,
            'explanation' => '"Wie alt sind Sie?" signifie "Quel âge avez-vous ?" en allemand.',
            'order' => 2,
        ]);

        Question::create([
            'quiz_id' => $quiz2->id,
            'question' => 'Quel est le nombre "zwölf" ?',
            'options' => ['11', '12', '13', '20'],
            'correct_answer' => 1,
            'explanation' => '"zwölf" signifie 12 en allemand.',
            'order' => 3,
        ]);
    }
}

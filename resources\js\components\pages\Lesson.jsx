import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import axios from 'axios';

const Lesson = () => {
    const { id } = useParams();
    const navigate = useNavigate();
    const [lesson, setLesson] = useState(null);
    const [loading, setLoading] = useState(true);
    const [completing, setCompleting] = useState(false);
    const [error, setError] = useState(null);

    useEffect(() => {
        fetchLesson();
    }, [id]);

    const fetchLesson = async () => {
        try {
            const response = await axios.get(`/api/lessons/${id}`);
            setLesson(response.data);
        } catch (error) {
            console.error('Error fetching lesson:', error);
            setError('Erreur lors du chargement de la leçon');
        } finally {
            setLoading(false);
        }
    };

    const handleCompleteLesson = async () => {
        setCompleting(true);
        try {
            await axios.post(`/api/lessons/${id}/complete`);
            // Refresh lesson data to get updated quiz info
            await fetchLesson();
        } catch (error) {
            console.error('Error completing lesson:', error);
            setError('Erreur lors de la validation de la leçon');
        } finally {
            setCompleting(false);
        }
    };

    if (loading) {
        return (
            <div className="min-h-screen flex items-center justify-center">
                <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-red-600"></div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="min-h-screen flex items-center justify-center">
                <div className="text-center">
                    <p className="text-red-600 mb-4">{error}</p>
                    <Link to="/dashboard" className="text-red-600 hover:text-red-800">
                        Retour au tableau de bord
                    </Link>
                </div>
            </div>
        );
    }

    if (!lesson) {
        return (
            <div className="min-h-screen flex items-center justify-center">
                <div className="text-center">
                    <p className="text-gray-600 mb-4">Leçon non trouvée</p>
                    <Link to="/dashboard" className="text-red-600 hover:text-red-800">
                        Retour au tableau de bord
                    </Link>
                </div>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-gray-50 py-8">
            <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
                {/* Header */}
                <div className="mb-8">
                    <Link 
                        to="/dashboard" 
                        className="text-red-600 hover:text-red-800 mb-4 inline-flex items-center"
                    >
                        ← Retour au tableau de bord
                    </Link>
                    
                    <div className="bg-white rounded-lg shadow-md p-6">
                        <div className="flex items-center justify-between mb-4">
                            <div>
                                <h1 className="text-3xl font-bold text-gray-900">
                                    {lesson.title}
                                </h1>
                                <p className="text-gray-600 mt-2">
                                    Niveau: {lesson.level}
                                </p>
                            </div>
                            
                            {/* German flag decoration */}
                            <div className="flex space-x-1">
                                <div className="w-3 h-12 bg-black rounded"></div>
                                <div className="w-3 h-12 bg-red-600 rounded"></div>
                                <div className="w-3 h-12 bg-yellow-400 rounded"></div>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Lesson Content */}
                <div className="bg-white rounded-lg shadow-md p-6 mb-8">
                    <h2 className="text-xl font-semibold mb-4 flex items-center">
                        <span className="mr-2">📖</span>
                        Contenu de la leçon
                    </h2>
                    
                    <div className="prose max-w-none">
                        <div className="whitespace-pre-wrap text-gray-700 leading-relaxed">
                            {lesson.content}
                        </div>
                    </div>

                    {/* Audio Section (Placeholder) */}
                    {lesson.audio_url && (
                        <div className="mt-6 p-4 bg-gray-50 rounded-lg">
                            <h3 className="text-lg font-medium mb-3 flex items-center">
                                <span className="mr-2">🔊</span>
                                Audio de la leçon
                            </h3>
                            <div className="flex items-center space-x-4">
                                <button className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md flex items-center">
                                    <span className="mr-2">▶️</span>
                                    Écouter
                                </button>
                                <span className="text-sm text-gray-600">
                                    Cliquez pour écouter la prononciation
                                </span>
                            </div>
                        </div>
                    )}
                </div>

                {/* Action Buttons */}
                <div className="bg-white rounded-lg shadow-md p-6">
                    <div className="flex flex-col sm:flex-row gap-4 justify-between items-center">
                        <div className="text-center sm:text-left">
                            <h3 className="text-lg font-semibold mb-2">
                                Prêt à tester vos connaissances ?
                            </h3>
                            <p className="text-gray-600">
                                Terminez la leçon puis passez au quiz pour valider vos acquis.
                            </p>
                        </div>
                        
                        <div className="flex flex-col sm:flex-row gap-3">
                            <button
                                onClick={handleCompleteLesson}
                                disabled={completing}
                                className="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-md font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                                {completing ? 'Validation...' : 'Marquer comme terminé'}
                            </button>
                            
                            {lesson.quiz && (
                                <Link
                                    to={`/quiz/${lesson.quiz.id}`}
                                    className="bg-red-600 hover:bg-red-700 text-white px-6 py-3 rounded-md font-medium transition-colors text-center"
                                >
                                    Faire le quiz
                                </Link>
                            )}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default Lesson;

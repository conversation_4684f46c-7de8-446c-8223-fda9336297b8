<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\UserProgress;
use App\Models\Lesson;
use Illuminate\Http\Request;

class UserProgressController extends Controller
{
    public function index(Request $request)
    {
        $user = $request->user();

        $progress = UserProgress::where('user_id', $user->id)
            ->with(['lesson', 'quiz'])
            ->get();

        $totalLessons = Lesson::active()->count();
        $completedLessons = $progress->where('lesson_completed', true)->where('quiz_completed', true)->count();

        $overallProgress = $totalLessons > 0 ? round(($completedLessons / $totalLessons) * 100) : 0;

        return response()->json([
            'progress' => $progress,
            'overall_progress' => $overallProgress,
            'total_lessons' => $totalLessons,
            'completed_lessons' => $completedLessons,
        ]);
    }

    public function show(Request $request, $lessonId)
    {
        $user = $request->user();

        $progress = UserProgress::where('user_id', $user->id)
            ->where('lesson_id', $lessonId)
            ->with(['lesson', 'quiz'])
            ->first();

        if (!$progress) {
            return response()->json([
                'lesson_completed' => false,
                'quiz_completed' => false,
                'quiz_score' => null,
                'quiz_attempts' => 0,
            ]);
        }

        return response()->json($progress);
    }
}

import React from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';

const Home = () => {
    const { isAuthenticated } = useAuth();

    return (
        <div className="min-h-screen bg-white">
            {/* Hero Section */}
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
                <div className="text-center">
                    {/* Message de bienvenue */}
                    <h1 className="text-5xl font-bold text-gray-900 mb-4">
                        Willkommen bei{' '}
                        <span className="text-red-600">LearnGermany</span>!
                    </h1>
                    
                    <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
                        Apprenez l'allemand facilement avec nos leçons interactives 
                        et nos quiz adaptés aux débutants.
                    </p>

                    {/* Illustration - Homme avec livre "Deutsch" */}
                    <div className="mb-12 flex justify-center">
                        <div className="relative">
                            {/* Silhouette d'homme simple */}
                            <div className="w-64 h-80 bg-gray-200 rounded-full relative mx-auto mb-4">
                                {/* Tête */}
                                <div className="w-16 h-16 bg-gray-400 rounded-full mx-auto mb-4 relative top-8"></div>
                                
                                {/* Corps */}
                                <div className="w-24 h-32 bg-gray-400 rounded-lg mx-auto relative top-4"></div>
                                
                                {/* Livre "Deutsch" */}
                                <div className="absolute top-24 left-1/2 transform -translate-x-1/2 rotate-12">
                                    <div className="w-20 h-16 bg-red-600 rounded shadow-lg flex items-center justify-center">
                                        <span className="text-white font-bold text-sm">DEUTSCH</span>
                                    </div>
                                </div>
                            </div>
                            
                            {/* Décoration avec couleurs allemandes */}
                            <div className="flex justify-center space-x-2 mt-4">
                                <div className="w-8 h-2 bg-black rounded"></div>
                                <div className="w-8 h-2 bg-red-600 rounded"></div>
                                <div className="w-8 h-2 bg-yellow-400 rounded"></div>
                            </div>
                        </div>
                    </div>

                    {/* Bouton d'action */}
                    <div className="space-y-4">
                        {isAuthenticated ? (
                            <Link
                                to="/dashboard"
                                className="inline-block bg-red-600 hover:bg-red-700 text-white font-bold py-4 px-8 rounded-lg text-lg transition-colors shadow-lg hover:shadow-xl transform hover:-translate-y-1"
                            >
                                Continuer l'apprentissage
                            </Link>
                        ) : (
                            <Link
                                to="/register"
                                className="inline-block bg-red-600 hover:bg-red-700 text-white font-bold py-4 px-8 rounded-lg text-lg transition-colors shadow-lg hover:shadow-xl transform hover:-translate-y-1"
                            >
                                Commencer à apprendre
                            </Link>
                        )}
                    </div>

                    {/* Fonctionnalités */}
                    <div className="mt-16 grid md:grid-cols-3 gap-8">
                        <div className="text-center p-6">
                            <div className="w-16 h-16 bg-black rounded-full mx-auto mb-4 flex items-center justify-center">
                                <span className="text-white text-2xl">📚</span>
                            </div>
                            <h3 className="text-xl font-semibold mb-2">Leçons structurées</h3>
                            <p className="text-gray-600">
                                Apprenez avec des leçons progressives adaptées aux débutants
                            </p>
                        </div>
                        
                        <div className="text-center p-6">
                            <div className="w-16 h-16 bg-red-600 rounded-full mx-auto mb-4 flex items-center justify-center">
                                <span className="text-white text-2xl">🎯</span>
                            </div>
                            <h3 className="text-xl font-semibold mb-2">Quiz interactifs</h3>
                            <p className="text-gray-600">
                                Testez vos connaissances avec des quiz amusants
                            </p>
                        </div>
                        
                        <div className="text-center p-6">
                            <div className="w-16 h-16 bg-yellow-400 rounded-full mx-auto mb-4 flex items-center justify-center">
                                <span className="text-white text-2xl">📈</span>
                            </div>
                            <h3 className="text-xl font-semibold mb-2">Suivi des progrès</h3>
                            <p className="text-gray-600">
                                Suivez votre progression et célébrez vos réussites
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default Home;

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_progress', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('lesson_id')->constrained()->onDelete('cascade');
            $table->foreignId('quiz_id')->nullable()->constrained()->onDelete('cascade');
            $table->boolean('lesson_completed')->default(false);
            $table->boolean('quiz_completed')->default(false);
            $table->integer('quiz_score')->nullable(); // Score du quiz (en %)
            $table->integer('quiz_attempts')->default(0); // Nombre de tentatives
            $table->timestamp('completed_at')->nullable();
            $table->timestamps();

            // Index unique pour éviter les doublons
            $table->unique(['user_id', 'lesson_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_progress');
    }
};

import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import axios from 'axios';

const Dashboard = () => {
    const { user } = useAuth();
    const [lessons, setLessons] = useState([]);
    const [userProgress, setUserProgress] = useState([]);
    const [loading, setLoading] = useState(true);
    const [overallProgress, setOverallProgress] = useState(0);

    useEffect(() => {
        fetchDashboardData();
    }, []);

    const fetchDashboardData = async () => {
        try {
            const [lessonsResponse, progressResponse] = await Promise.all([
                axios.get('/api/lessons'),
                axios.get('/api/user/progress')
            ]);
            
            setLessons(lessonsResponse.data);
            setUserProgress(progressResponse.data.progress || []);
            setOverallProgress(progressResponse.data.overall_progress || 0);
        } catch (error) {
            console.error('Error fetching dashboard data:', error);
        } finally {
            setLoading(false);
        }
    };

    const getLessonProgress = (lessonId) => {
        return userProgress.find(p => p.lesson_id === lessonId) || {
            lesson_completed: false,
            quiz_completed: false,
            quiz_score: null
        };
    };

    const getLessonStatus = (lessonId) => {
        const progress = getLessonProgress(lessonId);
        if (progress.quiz_completed) return 'completed';
        if (progress.lesson_completed) return 'quiz-ready';
        return 'not-started';
    };

    const getStatusText = (status) => {
        switch (status) {
            case 'completed': return 'Terminé';
            case 'quiz-ready': return 'Quiz disponible';
            case 'not-started': return 'Commencer';
            default: return 'Commencer';
        }
    };

    const getStatusColor = (status) => {
        switch (status) {
            case 'completed': return 'bg-green-600 hover:bg-green-700';
            case 'quiz-ready': return 'bg-yellow-600 hover:bg-yellow-700';
            case 'not-started': return 'bg-red-600 hover:bg-red-700';
            default: return 'bg-red-600 hover:bg-red-700';
        }
    };

    if (loading) {
        return (
            <div className="min-h-screen flex items-center justify-center">
                <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-red-600"></div>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-gray-50 py-8">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                {/* Header */}
                <div className="mb-8">
                    <h1 className="text-3xl font-bold text-gray-900">
                        Willkommen zurück, {user?.name}! 👋
                    </h1>
                    <p className="text-gray-600 mt-2">
                        Continuez votre apprentissage de l'allemand
                    </p>
                </div>

                {/* Progress Overview */}
                <div className="bg-white rounded-lg shadow-md p-6 mb-8">
                    <h2 className="text-xl font-semibold mb-4 flex items-center">
                        <span className="mr-2">📈</span>
                        Votre progression
                    </h2>
                    
                    <div className="mb-4">
                        <div className="flex justify-between text-sm text-gray-600 mb-1">
                            <span>Progression globale</span>
                            <span>{overallProgress}%</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-3">
                            <div 
                                className="bg-gradient-to-r from-red-600 to-yellow-400 h-3 rounded-full transition-all duration-300"
                                style={{ width: `${overallProgress}%` }}
                            ></div>
                        </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
                        <div className="text-center p-4 bg-gray-50 rounded-lg">
                            <div className="text-2xl font-bold text-red-600">
                                {userProgress.filter(p => p.lesson_completed).length}
                            </div>
                            <div className="text-sm text-gray-600">Leçons terminées</div>
                        </div>
                        <div className="text-center p-4 bg-gray-50 rounded-lg">
                            <div className="text-2xl font-bold text-yellow-600">
                                {userProgress.filter(p => p.quiz_completed).length}
                            </div>
                            <div className="text-sm text-gray-600">Quiz réussis</div>
                        </div>
                        <div className="text-center p-4 bg-gray-50 rounded-lg">
                            <div className="text-2xl font-bold text-green-600">
                                {userProgress.length > 0 
                                    ? Math.round(userProgress.reduce((acc, p) => acc + (p.quiz_score || 0), 0) / userProgress.filter(p => p.quiz_score).length) || 0
                                    : 0}%
                            </div>
                            <div className="text-sm text-gray-600">Score moyen</div>
                        </div>
                    </div>
                </div>

                {/* Lessons List */}
                <div className="bg-white rounded-lg shadow-md p-6">
                    <h2 className="text-xl font-semibold mb-6 flex items-center">
                        <span className="mr-2">📚</span>
                        Leçons disponibles
                    </h2>

                    {lessons.length === 0 ? (
                        <div className="text-center py-8 text-gray-500">
                            <p>Aucune leçon disponible pour le moment.</p>
                            <p className="text-sm mt-2">Revenez bientôt pour commencer votre apprentissage !</p>
                        </div>
                    ) : (
                        <div className="grid gap-4">
                            {lessons.map((lesson) => {
                                const status = getLessonStatus(lesson.id);
                                const progress = getLessonProgress(lesson.id);
                                
                                return (
                                    <div key={lesson.id} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                                        <div className="flex items-center justify-between">
                                            <div className="flex-1">
                                                <h3 className="text-lg font-medium text-gray-900">
                                                    {lesson.title}
                                                </h3>
                                                <p className="text-sm text-gray-600 mt-1">
                                                    Niveau: {lesson.level}
                                                </p>
                                                {progress.quiz_score && (
                                                    <p className="text-sm text-green-600 mt-1">
                                                        Score du quiz: {progress.quiz_score}%
                                                    </p>
                                                )}
                                            </div>
                                            
                                            <div className="flex items-center space-x-3">
                                                {status === 'completed' && (
                                                    <span className="text-green-600 text-xl">✅</span>
                                                )}
                                                {status === 'quiz-ready' && (
                                                    <span className="text-yellow-600 text-xl">⚡</span>
                                                )}
                                                
                                                <Link
                                                    to={`/lesson/${lesson.id}`}
                                                    className={`px-4 py-2 rounded-md text-white text-sm font-medium transition-colors ${getStatusColor(status)}`}
                                                >
                                                    {getStatusText(status)}
                                                </Link>
                                            </div>
                                        </div>
                                    </div>
                                );
                            })}
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
};

export default Dashboard;

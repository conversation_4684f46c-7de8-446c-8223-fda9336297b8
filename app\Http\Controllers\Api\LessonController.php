<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Lesson;
use App\Models\UserProgress;
use Illuminate\Http\Request;

class LessonController extends Controller
{
    public function index()
    {
        $lessons = Lesson::active()
            ->ordered()
            ->get();

        return response()->json($lessons);
    }

    public function show($id)
    {
        $lesson = Lesson::with('quiz.questions')->findOrFail($id);

        return response()->json($lesson);
    }

    public function markAsCompleted(Request $request, $id)
    {
        $lesson = Lesson::findOrFail($id);
        $user = $request->user();

        $progress = UserProgress::updateOrCreate(
            [
                'user_id' => $user->id,
                'lesson_id' => $lesson->id,
            ],
            [
                'lesson_completed' => true,
                'quiz_id' => $lesson->quiz?->id,
            ]
        );

        return response()->json([
            'message' => 'Leçon marquée comme terminée',
            'progress' => $progress
        ]);
    }
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('questions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('quiz_id')->constrained()->onDelete('cascade');
            $table->text('question'); // La question
            $table->json('options'); // Les options de réponse (array JSON)
            $table->integer('correct_answer'); // Index de la bonne réponse (0, 1, 2, etc.)
            $table->text('explanation')->nullable(); // Explication de la réponse
            $table->integer('order')->default(0); // Ordre dans le quiz
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('questions');
    }
};

<?php

use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\LessonController;
use App\Http\Controllers\Api\QuizController;
use App\Http\Controllers\Api\UserProgressController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

// Routes d'authentification
Route::post('/register', [AuthController::class, 'register']);
Route::post('/login', [AuthController::class, 'login']);

// Routes protégées par authentification
Route::middleware('auth:sanctum')->group(function () {
    // Authentification
    Route::post('/logout', [AuthController::class, 'logout']);
    Route::get('/user', [AuthController::class, 'user']);

    // Leçons
    Route::get('/lessons', [LessonController::class, 'index']);
    Route::get('/lessons/{id}', [LessonController::class, 'show']);
    Route::post('/lessons/{id}/complete', [LessonController::class, 'markAsCompleted']);

    // Quiz
    Route::get('/quiz/{id}', [QuizController::class, 'show']);
    Route::post('/quiz/{id}/submit', [QuizController::class, 'submit']);

    // Progrès utilisateur
    Route::get('/user/progress', [UserProgressController::class, 'index']);
    Route::get('/user/progress/{lessonId}', [UserProgressController::class, 'show']);
});

<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Quiz;
use App\Models\Question;
use App\Models\UserProgress;
use Illuminate\Http\Request;

class QuizController extends Controller
{
    public function show($id)
    {
        $quiz = Quiz::with(['questions' => function($query) {
            $query->ordered();
        }])->findOrFail($id);

        // Remove correct answers from questions for security
        $quiz->questions->transform(function ($question) {
            return [
                'id' => $question->id,
                'question' => $question->question,
                'options' => $question->options,
                'order' => $question->order,
            ];
        });

        return response()->json($quiz);
    }

    public function submit(Request $request, $id)
    {
        $request->validate([
            'answers' => 'required|array',
            'answers.*' => 'required|integer|min:0',
        ]);

        $quiz = Quiz::with('questions')->findOrFail($id);
        $user = $request->user();
        $answers = $request->answers;

        // Calculate score
        $totalQuestions = $quiz->questions->count();
        $correctAnswers = 0;

        $results = [];
        foreach ($quiz->questions as $question) {
            $userAnswer = $answers[$question->id] ?? null;
            $isCorrect = $question->isCorrectAnswer($userAnswer);

            if ($isCorrect) {
                $correctAnswers++;
            }

            $results[] = [
                'question_id' => $question->id,
                'question' => $question->question,
                'user_answer' => $userAnswer,
                'correct_answer' => $question->correct_answer,
                'is_correct' => $isCorrect,
                'explanation' => $question->explanation,
            ];
        }

        $score = $totalQuestions > 0 ? round(($correctAnswers / $totalQuestions) * 100) : 0;
        $passed = $score >= $quiz->passing_score;

        // Update user progress
        $progress = UserProgress::where('user_id', $user->id)
            ->where('lesson_id', $quiz->lesson_id)
            ->first();

        if ($progress) {
            $progress->update([
                'quiz_completed' => $passed,
                'quiz_score' => $score,
                'quiz_attempts' => $progress->quiz_attempts + 1,
                'completed_at' => $passed ? now() : $progress->completed_at,
            ]);
        }

        return response()->json([
            'score' => $score,
            'passed' => $passed,
            'passing_score' => $quiz->passing_score,
            'correct_answers' => $correctAnswers,
            'total_questions' => $totalQuestions,
            'results' => $results,
        ]);
    }
}

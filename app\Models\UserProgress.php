<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UserProgress extends Model
{
    protected $fillable = [
        'user_id',
        'lesson_id',
        'quiz_id',
        'lesson_completed',
        'quiz_completed',
        'quiz_score',
        'quiz_attempts',
        'completed_at',
    ];

    protected $casts = [
        'lesson_completed' => 'boolean',
        'quiz_completed' => 'boolean',
        'completed_at' => 'datetime',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function lesson(): BelongsTo
    {
        return $this->belongsTo(Lesson::class);
    }

    public function quiz(): BelongsTo
    {
        return $this->belongsTo(Quiz::class);
    }

    public function scopeCompleted($query)
    {
        return $query->where('lesson_completed', true)
                    ->where('quiz_completed', true);
    }

    public function getProgressPercentageAttribute(): int
    {
        $progress = 0;
        if ($this->lesson_completed) $progress += 50;
        if ($this->quiz_completed) $progress += 50;
        return $progress;
    }
}
